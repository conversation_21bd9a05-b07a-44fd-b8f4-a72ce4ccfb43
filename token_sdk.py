import base64
import datetime
import json
import re
import traceback
from time import sleep
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed

environment_variables={}

session = requests.Session()
class TokenSDK:
    def __init__(self):
        """
        :param forgeRock_url: 获取token的API URL
        """
        self.forgeRock_url = "https://int.identity.jaguarlandrover.cn"


    def refresh_token(self, f_type=0):
        """
        用户需要强制refreshToken
        """
        try:
            phone_number = "18616586957"
            if f_type == 1:
                phone_number = "16628730934"
            db_res = self.get_db_token(phone_number)
            if db_res.get('msg') == "success":
                refresh_token = db_res.get('refresh_token')
                access_token = db_res.get('access_token')
                status = db_res.get('status')
                expire_time_old = db_res.get('expires_time')
                if_update = False
                while status == "updating":
                    print("updating, wait!")
                    if_update = True
                    sleep(1)
                    db_res = self.get_db_token(phone_number)
                    if db_res.get('msg') == "success":
                        status = db_res.get('status')
                        access_token = db_res.get('access_token')

                # 如果没有更新过，则status置为updating，后续调用refreshToken接口；如果更新过了，则直接返回新的access_token，无需再调用
                if not if_update:
                    res = self.update_token_status(phone_number, "updating", expire_time_old)
                    if not res:
                        db_res = self.get_db_token(phone_number)
                        if db_res.get('msg') == "success":
                            status = db_res.get('status')
                            expire_time = db_res.get('expires_time')
                            while status == "updating":
                                sleep(1)
                                db_res = self.get_db_token(phone_number)
                                if db_res.get('msg') == "success":
                                    status = db_res.get('status')
                                    expire_time = db_res.get('expires_time')
                                    access_token = db_res.get('access_token')
                            if expire_time > expire_time_old:
                                print("no need to refresh token again")
                                return {
                                        "access_token": access_token,
                                        "msg": "success",
                                        "errCode": 0
                                    }
                else:
                    return {
                    "access_token": access_token,
                    "msg": "success",
                    "errCode": 0
                }
            else:
                return {
                    "access_token": "",
                    "msg": "refreshToken failed because get refresh_token from db failed",
                    "errCode": 1
                }

            url = "https://int.identity.jaguarlandrover.cn/gateway/oauth2/realms/root/realms/customer/access_token?grant_type=refresh_token&client_id=oneapp-rangerover&refresh_token={}".format(refresh_token)
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
            }
            res = requests.post(url, headers=headers,verify=False)
            print("refreshToken res: " + res.text + '\r\n')
            if res.status_code == 200:
                response = res.json()
                access_token = response['access_token']
                refresh_token = response['refresh_token']
                expires_in = response['expires_in']
                expire_time = datetime.datetime.now() + datetime.timedelta(seconds=expires_in)
                expire_time_ts = int(expire_time.timestamp()) * 1000
                # update token to db
                res = self.update_token(phone_number, access_token, refresh_token, expire_time_ts, 1, "success")
                return {
                    "access_token": access_token,
                    "msg": "success",
                    "errCode": 0
                }
            else:
                res = self.update_token_status(phone_number,"fail", expire_time_old)
                return {
                    "access_token": "",
                    "msg": "refreshToken failed.",
                    "errCode": 1
                }
        except Exception as e:
            res = self.update_token_status(phone_number, "fail", expire_time_old)
            traceback.print_exc()
            return {
                "access_token": "",
                "msg": "refreshToken failed due to exception.",
                "errCode": 2
            }

    def get_token(self, f_type=0):
        """
        获取访问token，或者重新生成新的
        type = 0 : non-wireless
        type = 1: wireless
        :return: token字符串
        """
        # 第一步查看是否已有token且token还在五分钟有效期内则直接返回
        try:
            phone_number = "18616586957"
            if f_type == 1:
                phone_number = "16628730934"
            db_res = self.get_db_token(phone_number)
            if db_res.get('msg') == "success":
                expires_time = db_res.get('expires_time')
                refresh_token = db_res.get('refresh_token')
                access_token = db_res.get('access_token')
                # 有效期内直接返回
                if expires_time > int((datetime.datetime.now()).timestamp()) * 1000:
                    print ("no need to refresh.")
                    return {
                        "access_token": access_token,
                        "msg": "success",
                        "errCode": 0
                    }
                # refreshToken后返回新的access token
                else:
                    res = self.refresh_token(f_type)
                    access_token = res['access_token']
                    return {
                        "access_token": access_token,
                        "msg": "success",
                        "errCode": 0
                    }

            # 若没有token，则调用全链路生成新token并存入且返回用户
            access_token = self.get_new_forgeRock_token(phone_number)
            if access_token:
                return {
                    "access_token": access_token,
                    "msg": "success",
                    "errCode": 0
                }
            else:
                return {
                    "access_token": "",
                    "msg": "fetch token failed.",
                    "errCode": 1
                }
        except Exception as e:
            traceback.print_exc()
            return {
                "access_token": "",
                "msg": "fetch token failed due to exception.",
                "errCode": 2
            }

    def get_new_forgeRock_token(self, f_type=0):
        if f_type == 1:
            phone_number = "16628730934"
        elif f_type == 0:
            phone_number = "18616586957"
        first_res = self.smsotp_start()
        if first_res:
            print("first step passed.")
            second_res = self.smsotp_sendsms(phone_number)
            if second_res:
                print("second step passed.")
                self.smsotp_continue_reject()
                print("third step passed.")
                # 获取短信验证码
                sms_code = self.get_sms_code(phone_number)
                if sms_code:
                    global environment_variables
                    environment_variables['sms_code'] = sms_code
                    # 第四步
                    four_res = self.smsotp_get_token()
                    print(sms_code, four_res)
                    if four_res:
                        print("fourth step passed.")
                        # 第五步
                        fifth_res = self.smsotp_get_authz_code()
                        if fifth_res:
                            print("fifth step passed.")
                            access_token, refresh_token, expires_in = self.smsotp_get_access_token()
                            if access_token and refresh_token:
                                print("-----------------------------------------------")
                                expire_time = datetime.datetime.now() + datetime.timedelta(seconds=expires_in)
                                #expire_time = expire_time_ts.strftime('%Y-%m-%d %H:%M:%S')
                                expire_time_ts = int(expire_time.timestamp()) * 1000
                                print("access_token: " + access_token)
                                print("refresh_token: " + refresh_token)
                                res = self.update_token(phone_number, access_token, refresh_token, expire_time_ts, 1, "success")
                                print("Create token successfully.")
                                return access_token
        return ""

    def smsotp_start(self):
        url = "https://int.identity.jaguarlandrover.cn/gateway/json/realms/root/realms/customer/authenticate?service=service=main-landing&authIndexType=service&authIndexValue=main-landing"
        payload = ""
        res = session.post(url, data=payload,verify=False)
        res.raise_for_status()
        response = res.json()
        # post script
        try:
            auth_id = response.get("authId")
            assert auth_id is not None, "authId is missing in the response"
        except AssertionError as e:
            print(f"断言失败: {e}")
            return False
        global environment_variables
        environment_variables['authId'] = auth_id
        global collection_variables
        collection_variables = {}
        collection_variables['authId'] = auth_id
        return True

    def smsotp_sendsms(self, phone_number):
        url = "https://int.identity.jaguarlandrover.cn/gateway/json/realms/root/realms/customer/authenticate?service=service=main-landing&authIndexType=service&authIndexValue=main-landing"

        payload = json.dumps({
            "authId": environment_variables['authId'],
            "callbacks": [
                {
                    "type": "ValidatedCreateUsernameCallback",
                    "output": [
                        {
                            "name": "policies",
                            "value": {
                                "policyRequirements": [
                                    "MATCH_REGEXP",
                                    "VALID_TYPE"
                                ],
                                "fallbackPolicies": None,
                                "name": "mobileNumber",
                                "policies": [
                                    {
                                        "policyRequirements": [
                                            "MATCH_REGEXP"
                                        ],
                                        "policyId": "regexpMatches",
                                        "params": {
                                            "regexp": "^\\+861[0-9]{10}$"
                                        }
                                    },
                                    {
                                        "policyRequirements": [
                                            "VALID_TYPE"
                                        ],
                                        "policyId": "valid-type",
                                        "params": {
                                            "types": [
                                                "string"
                                            ]
                                        }
                                    }
                                ],
                                "conditionalPolicies": None
                            }
                        },
                        {
                            "name": "failedPolicies",
                            "value": []
                        },
                        {
                            "name": "validateOnly",
                            "value": False
                        },
                        {
                            "name": "prompt",
                            "value": "Mobile Number"
                        }
                    ],
                    "input": [
                        {
                            "name": "IDToken1",
                            "value": "+86" + phone_number
                        },
                        {
                            "name": "IDToken1validateOnly",
                            "value": False
                        }
                    ],
                    "_id": 0
                }
            ],
            "stage": "sms-mobileNumber-collector"
        })
        headers = {
            'accept-api-version': 'protocol=1.0,resource=2.1',
            'content-type': 'application/json'
        }
        res = session.post(url, data=payload, headers=headers,verify=False)
        res.raise_for_status()
        response = res.json()
        # post script
        try:
            assert res.status_code == 200, "Response status code is not 200"

            assert isinstance(response, dict), "Response is not a JSON object"
            assert 'authId' in response, "Response is missing 'authId' field"
            assert 'callbacks' in response, "Response is missing 'callbacks' field"
            assert 'stage' in response, "Response is missing 'stage' field"

            assert isinstance(response['callbacks'], list), "Callbacks is not an array"
            assert len(response['callbacks']) >= 1, "Callbacks array is empty"

            for callback in response['callbacks']:
                assert isinstance(callback['output'], list), "Output is not an array"
                assert len(callback['output']) >= 1, "Output array is empty"

                for output in callback['output']:
                    assert isinstance(output, dict), "Output is not an object"
                    assert 'name' in output, "Output is missing 'name' field"
                    assert isinstance(output['name'], str), "Name is not a string"
                    assert 'value' in output, "Output is missing 'value' field"
        except AssertionError as e:
            print(f"断言失败: {e}")
            return False
        return True

    def smsotp_continue_reject(self):
        url = "https://int.identity.jaguarlandrover.cn/gateway/json/realms/root/realms/customer/authenticate?service=service=main-landing&authIndexType=service&authIndexValue=main-landing"

        payload = json.dumps({
            "authId": environment_variables['authId'],
            "callbacks": [
                {
                    "type": "TextOutputCallback",
                    "output": [
                        {
                            "name": "message",
                            "value": "fr_smsotp_sent"
                        },
                        {
                            "name": "messageType",
                            "value": "0"
                        }
                    ],
                    "_id": 1
                },
                {
                    "type": "ConfirmationCallback",
                    "output": [
                        {
                            "name": "prompt",
                            "value": ""
                        },
                        {
                            "name": "messageType",
                            "value": 0
                        },
                        {
                            "name": "options",
                            "value": [
                                "fr_smsotp_continue",
                                "fr_smsotp_reject"
                            ]
                        },
                        {
                            "name": "optionType",
                            "value": -1
                        },
                        {
                            "name": "defaultOption",
                            "value": 1
                        }
                    ],
                    "input": [
                        {
                            "name": "IDToken2",
                            "value": "0"
                        }
                    ],
                    "_id": 2
                }
            ],
            "stage": "fr-smsotp-continueReject"
        })
        headers = {
            'accept-api-version': 'protocol=1.0,resource=2.1',
            'content-type': 'application/json'
        }
        res = session.post(url, data=payload, headers=headers)
        res.raise_for_status()
        response = res.json()

    def get_sms_code(self, phone_number):
        url = "https://portal.jlr-dpp-vv.cn/get_forge_sms"
        payload = json.dumps({
            "phoneNumber": "+86" + phone_number
        })
        headers = {
            'Content-Type': 'application/json',
            'X-VCDP-TOKEN': 'vcdpseit'
        }
        #response = {}
        try:
            expired_time = datetime.datetime.now()
            current_time = expired_time
            cnt = 0
            code = "933493"
            # while current_time <= expired_time and cnt < 10:
            while current_time >= expired_time and cnt < 30 or code == "101266" :
                sleep(3)
                res = requests.get(url, headers=headers, data=payload)
                cnt += 1
                print("retry " + str(cnt) + " times.....")
                res.raise_for_status()
                response = res.json()
                expired_time_ms = response.get("data", {}).get('expired_time')
                if expired_time_ms:
                    timestamp_s = expired_time_ms / 1000.0
                    expired_time = datetime.datetime.fromtimestamp(timestamp_s)
                    current_time = datetime.datetime.now()
                    code = response.get("data", {}).get('sms_code')
                else:
                    print("no sms code returned")
                    break
        except Exception as e:
            print(e)
            return ""
        return response.get("data", {}).get('sms_code')

    def smsotp_get_token(self):
        global environment_variables
        url = "https://int.identity.jaguarlandrover.cn/gateway/json/realms/root/realms/customer/authenticate?service=service=main-landing&authIndexType=service&authIndexValue=main-landing"
        payload = json.dumps({
            "authId": environment_variables['authId'],
            "callbacks": [
                {
                    "type": "PasswordCallback",
                    "output": [
                        {
                            "name": "prompt",
                            "value": "One Time Password"
                        }
                    ],
                    "input": [
                        {
                            "name": "IDToken1",
                            "value": environment_variables['sms_code']
                        }
                    ],
                    "_id": 0
                }
            ],
            "stage": "sms-otp-collector"
        })
        headers = {
            'accept-api-version': 'protocol=1.0,resource=2.1',
            'content-type': 'application/json',
        }

        res = session.post(url, headers=headers, data=payload)
        res.raise_for_status()
        response = res.json()

        # post script
        try:
            cookie = response['tokenId']
            if cookie:
                environment_variables['customerCookie'] = cookie
                return True
            else:
                return False
        except Exception as e:
            print(e)
            return False

    def smsotp_get_authz_code(self):
        url = "https://int.identity.jaguarlandrover.cn/gateway/oauth2/realms/root/realms/customer/authorize"
        global environment_variables
        payload = 'redirect_uri=oneapp-rangerover%3A%2F%2Foauth2redirect&client_id=oneapp-rangerover&response_type=code&nonce=1234&state=abc123&scope=openid%20email%20profile%20urn%3Aiam2-mgd-v1%3Ascopes%3Avehicle%3Avehicle-identity%20urn%3Aiam2-mgd-v1%3Ascopes%3Acustomer%3Aperson&code_challenge=j3wKnK2Fa_mc2tgdqa6GtUfCYjdWSA5S23JKTTtPF8Y&code_challenge_method=S256&decision=allow&csrf=' + environment_variables['customerCookie']
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
        res = session.post(url, headers=headers, data=payload, allow_redirects=False)

        # post script
        try:
            assert res.status_code < 500, "Response is an error"
            # assert res.headers.get('Content-Type') == 'application/json', "Response does not include a JSON body"
            assert res.status_code == 302, "Status code is not 302"
            location = res.headers.get("Location")
            assert location is not None, "Location header is missing"

            # 使用正则表达式提取code
            match = re.search(r"(?<=code=)[^&]+", location)
            assert match is not None, "Code is missing in the Location header"
            code = match.group(0)
            environment_variables['customerCode'] = code
        except AssertionError as e:
            print(f"断言失败: {e}")
            return False
        return True

    def smsotp_get_access_token(self):
        global environment_variables
        url = "https://int.identity.jaguarlandrover.cn/gateway/oauth2/realms/root/realms/customer/access_token"
        payload = 'grant_type=authorization_code&client_id=oneapp-rangerover&redirect_uri=oneapp-rangerover%3A%2F%2Foauth2redirect&code_verifier=ZpJiIM_G0SE9WlxzS69Cq0mQh8uyFaeEbILlW8tHs62SmEE6n7Nke0XJGx_F4OduTI4&code=' + environment_variables['customerCode']
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
        res = session.post(url, headers=headers, data=payload)
        res.raise_for_status()
        response = res.json()

        # post script
        try:
            assert res.status_code < 500, "Response is an error"
            # assert res.headers.get('Content-Type') == 'application/json', "Response does not include a JSON body"
            assert res.status_code == 200, "Status code is not 200"
            assert 'access_token' in response, "Response body does not include 'access_token'"
            assert isinstance(response['access_token'], str), "access_token is not a string"
            environment_variables['customerAccessToken'] = response['access_token']
            assert 'refresh_token' in response, "Response body does not include 'refresh_token'"
            assert isinstance(response['refresh_token'], str), "refresh_token is not a string"
            environment_variables['customerRefreshToken'] = response['refresh_token']
            assert 'id_token' in response, "Response body does not include 'id_token'"
            assert isinstance(response['id_token'], str), "id_token is not a string"
            environment_variables['customerIdToken'] = response['id_token']
            # decode
            id_token = response['id_token']
            id_token_payload = id_token.split('.')[1]
            decoded_id_token_payload = base64.b64decode(id_token_payload + '===').decode('utf-8')
            parsed_id_token_payload = json.loads(decoded_id_token_payload)
            print(f"Decoded ID token payload: {parsed_id_token_payload}")
            assert 'token_type' in response, "Response body does not include 'token_type'"
            assert isinstance(response['token_type'], str), "token_type is not a string"
            assert response['token_type'] == "Bearer", "token_type is not 'Bearer'"
            assert 'expires_in' in response, "Response body does not include 'expires_in'"
            assert isinstance(response['expires_in'], int), "expires_in is not a number"
            assert response['expires_in'] > 0, "expires_in is not greater than 0"
        except AssertionError as e:
            print(f"断言失败: {e}")
            return "", "", ""
        return response['access_token'], response['refresh_token'], response['expires_in']

    def update_token(self, phone_number, access_token, refresh_token, expires_time, code_type, status):
        url = "https://portal.jlr-dpp-vv.cn/updateForgeRockToken"
        payload = json.dumps({
            "phoneNumber": "+86" + phone_number,
            "codeType": code_type,
            "accessToken": access_token,
            "refreshToken": refresh_token,
            "expireTime": expires_time,
            "status": status
        })
        headers = {
            'Content-Type': 'application/json',
            'X-VCDP-TOKEN': 'vcdpseit'
        }
        res = requests.post(url, data=payload, headers=headers)
        response = json.loads(res.text)
        print("updateForgeRockToken res:" + str(response))
        if res.status_code == 200 and response["data"] == 1:
            return True
        else:
            print("Update token to db failed.")
            return False

    def update_token_status(self, phone_number, status, expire_time):
        url = "https://portal.jlr-dpp-vv.cn/updateTokenStatus"
        payload = json.dumps({
            "phoneNumber": "+86" + phone_number,
            "status": status,
            "expireTime": expire_time
        })
        headers = {
            'Content-Type': 'application/json',
            'X-VCDP-TOKEN': 'vcdpseit'
        }
        res = requests.post(url, data=payload, headers=headers)
        response = json.loads(res.text)
        if res.status_code == 200 and "data" in response and response["data"] == 1:
            return True
        else:
            print("Update token status to db failed for " + status)
            return False

    def get_db_token(self, phone_number):
        url = "https://portal.jlr-dpp-vv.cn/getForgeRockToken"
        payload = json.dumps({
            "phoneNumber": "+86" + phone_number
        })
        headers = {
            'Content-Type': 'application/json',
            'X-VCDP-TOKEN': 'vcdpseit'
        }
        res = requests.get(url, data=payload, headers=headers)
        if res.status_code == 200:
            response = res.json()
            if response["data"] != {}:
                access_token = response['data']['access_token']
                refresh_token = response['data']['refresh_token']
                expires_time = response['data']['expired_time']
                status = response['data']['status']
                return {
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "expires_time": expires_time,
                    "status": status,
                    "msg": "success"
                }
        print("get token from db failed.")
        return {
            "access_token": "",
            "refresh_token": "",
            "expires_time": "",
            "status": "",
            "msg": "failed"
        }


if __name__ == "__main__":
    token_sdk = TokenSDK()

    try:
        # with ThreadPoolExecutor(max_workers=2) as executor:
        #     cnt = 0
        #     while cnt < 1:
        #         futures = [
        #             executor.submit(token_sdk.refresh_token),
        #             executor.submit(token_sdk.refresh_token),
        #             # executor.submit(token_sdk.update_token_status, "18616586957", "success"),
        #             # executor.submit(token_sdk.update_token_status, "18616586957", "success"),
        #         ]
        #         sleep(1)
        #         for future in as_completed(futures):
        #             try:
        #                 result = future.result()
        #                 print("final result:" + str(result))
        #             except Exception as e:
        #                 print(f"Error: {e}")
        #         cnt += 1
        # for example
        # to get non-wireless token
        #token = token_sdk.get_token() # 972936b8-4372-47a7-93fb-b3ce7ba53ab1
        # to get wireless token
        token = token_sdk.get_token(1) # 38feaa77-8046-432b-baac-c118da3ced75

        # to refresh non-wireless token
        # token = token_sdk.refresh_token()
        # to refresh wireless token
        # token = token_sdk.refresh_token(1)

        # token = token_sdk.get_new_forgeRock_token()
        print(token["access_token"])
    except Exception as e:
        print("Error:", e)
